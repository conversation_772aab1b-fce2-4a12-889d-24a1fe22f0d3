<?php
// =================================================================
// Script: Descargar Documento InteletGroup
// Descripción: Sirve archivos de forma segura con autenticación
// =================================================================

// Configuración de seguridad
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Headers de seguridad
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    http_response_code(401);
    die('No autorizado');
}

// Verificar parámetros
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    die('Parámetro inválido');
}

$documento_id = (int)$_GET['id'];
$usuario_id = $_SESSION['usuario_id'];
$action = $_GET['action'] ?? 'download';

// Log inicial para debugging
error_log("=== INICIO DESCARGA DOCUMENTO ===");
error_log("Documento ID: $documento_id");
error_log("Usuario ID: $usuario_id");
error_log("Acción: $action");
error_log("User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A'));
error_log("IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'N/A'));

// Conectar a la base de datos
require_once 'con_db.php';

if (!isset($mysqli) || $mysqli->connect_error) {
    http_response_code(500);
    die('Error de conexión a la base de datos');
}

// Obtener el rol del usuario para verificar permisos
$sql_rol = "SELECT rol FROM tb_experian_usuarios WHERE id = ? AND proyecto = 'inteletGroup'";
$stmt_rol = $mysqli->prepare($sql_rol);
if (!$stmt_rol) {
    error_log("Error preparando consulta de rol: " . $mysqli->error);
    http_response_code(500);
    die('Error en la consulta');
}

$stmt_rol->bind_param("i", $usuario_id);
$stmt_rol->execute();
$stmt_rol->bind_result($rol_usuario);

if (!$stmt_rol->fetch()) {
    $stmt_rol->close();
    error_log("Usuario no encontrado o sin permisos - Usuario ID: $usuario_id");
    http_response_code(403);
    die('Sin permisos');
}
$stmt_rol->close();

error_log("Rol del usuario: $rol_usuario");

// Construir consulta SQL según el rol del usuario
if ($rol_usuario === 'admin') {
    // Los administradores pueden acceder a todos los documentos
    $sql = "SELECT d.id, d.prospecto_id, d.usuario_id, d.rut_cliente, d.nombre_archivo,
                   d.nombre_original, d.tipo_archivo, d.tamaño_archivo, d.ruta_archivo,
                   d.fecha_subida, d.estado, p.usuario_id as propietario_id
            FROM tb_inteletgroup_documentos d
            INNER JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id
            WHERE d.id = ? AND d.estado = 'Activo'";
} else {
    // Los ejecutivos solo pueden acceder a documentos de sus prospectos
    $sql = "SELECT d.id, d.prospecto_id, d.usuario_id, d.rut_cliente, d.nombre_archivo,
                   d.nombre_original, d.tipo_archivo, d.tamaño_archivo, d.ruta_archivo,
                   d.fecha_subida, d.estado, p.usuario_id as propietario_id
            FROM tb_inteletgroup_documentos d
            INNER JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id
            WHERE d.id = ? AND p.usuario_id = ? AND d.estado = 'Activo'";
}

$stmt = $mysqli->prepare($sql);
if (!$stmt) {
    error_log("Error preparando consulta: " . $mysqli->error);
    http_response_code(500);
    die('Error en la consulta');
}

// Bind parameters según el rol del usuario
if ($rol_usuario === 'admin') {
    // Los administradores solo necesitan el ID del documento
    $stmt->bind_param("i", $documento_id);
} else {
    // Los ejecutivos necesitan ID del documento y su usuario_id
    $stmt->bind_param("ii", $documento_id, $usuario_id);
}

$stmt->execute();

// Usar bind_result para compatibilidad con PHP más antiguo - orden correcto según la consulta SQL
$stmt->bind_result($doc_id, $prospecto_id, $doc_usuario_id, $rut_cliente, $nombre_archivo,
                   $nombre_original, $tipo_archivo, $tamaño_archivo, $ruta_archivo,
                   $fecha_subida, $estado, $propietario_id);

if (!$stmt->fetch()) {
    $stmt->close();
    error_log("Documento no encontrado - ID: $documento_id, Usuario: $usuario_id");
    http_response_code(404);
    die('Documento no encontrado');
}

$documento = [
    'id' => $doc_id,
    'prospecto_id' => $prospecto_id,
    'usuario_id' => $doc_usuario_id,
    'rut_cliente' => $rut_cliente,
    'nombre_archivo' => $nombre_archivo,
    'nombre_original' => $nombre_original,
    'tipo_archivo' => $tipo_archivo,
    'tamaño_archivo' => $tamaño_archivo,
    'ruta_archivo' => $ruta_archivo,
    'fecha_subida' => $fecha_subida,
    'estado' => $estado,
    'propietario_id' => $propietario_id
];

// Log para debugging
error_log("Documento encontrado - ID: {$documento['id']}, Archivo: {$documento['nombre_original']}, Ruta: {$documento['ruta_archivo']}");

$stmt->close();

// Verificar que el archivo existe - Mejorar manejo de rutas
$file_path = $documento['ruta_archivo'];
$original_path = $file_path;

// Log inicial para debugging
error_log("Verificando archivo - Ruta original: $original_path");

// Si la ruta no es absoluta, construir la ruta completa
if (!file_exists($file_path)) {
    // Intentar con ruta relativa desde el directorio actual
    $file_path = __DIR__ . '/' . $documento['ruta_archivo'];
    error_log("Intentando ruta relativa: $file_path");

    if (!file_exists($file_path)) {
        // Intentar sin el directorio actual si la ruta ya incluye uploads/
        if (strpos($documento['ruta_archivo'], 'uploads/') === 0) {
            $file_path = __DIR__ . '/' . $documento['ruta_archivo'];
        } else {
            // Intentar construyendo la ruta completa con uploads/
            $file_path = __DIR__ . '/uploads/inteletgroup_prospectos/' . basename($documento['ruta_archivo']);
        }
        error_log("Intentando ruta construida: $file_path");

        if (!file_exists($file_path)) {
            // Log detallado para debug
            error_log("ARCHIVO NO ENCONTRADO:");
            error_log("- Ruta original: " . $documento['ruta_archivo']);
            error_log("- Ruta relativa: " . __DIR__ . '/' . $documento['ruta_archivo']);
            error_log("- Ruta construida: " . $file_path);
            error_log("- Directorio actual: " . __DIR__);
            error_log("- Archivo existe en ruta original: " . (file_exists($original_path) ? 'SI' : 'NO'));

            http_response_code(404);
            die('Archivo no encontrado en el servidor');
        }
    }
}

error_log("Archivo encontrado en: $file_path");

// Obtener información del archivo
$file_size = filesize($file_path);
$file_name = $documento['nombre_original'];
$mime_type = $documento['tipo_archivo'];

// Log detallado para debugging
error_log("Información del archivo:");
error_log("- Ruta final: $file_path");
error_log("- Tamaño: $file_size bytes");
error_log("- Nombre original: $file_name");
error_log("- Tipo MIME: $mime_type");
error_log("- Acción: $action");

// Validar tipo MIME para seguridad
$allowed_types = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'application/rtf',
    'application/zip',
    'application/x-zip-compressed'
];

// Log del tipo MIME para debugging
error_log("Validando tipo MIME: '$mime_type' para documento ID: {$documento['id']}");

if (!in_array($mime_type, $allowed_types)) {
    error_log("Tipo MIME no permitido: '$mime_type' para documento ID: {$documento['id']}");
    http_response_code(403);
    die('Tipo de archivo no permitido: ' . $mime_type);
}

// Configurar headers según la acción
if ($action === 'view') {
    // Para visualización en el navegador
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: inline; filename="' . addslashes($file_name) . '"');
    header('X-Content-Type-Options: nosniff');
} else {
    // Para descarga
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . addslashes($file_name) . '"');
}

// Headers comunes
header('Content-Length: ' . $file_size);
header('Cache-Control: private, must-revalidate');
header('Pragma: private');
header('Expires: 0');

// Log antes de servir el archivo
error_log("Sirviendo archivo - Headers configurados, iniciando readfile()");

// Limpiar buffer de salida
if (ob_get_level()) {
    ob_end_clean();
}

// Verificar una vez más que el archivo existe antes de servirlo
if (!file_exists($file_path)) {
    error_log("ERROR CRÍTICO: Archivo desapareció antes de servirlo: $file_path");
    http_response_code(404);
    die('Archivo no encontrado');
}

// Servir el archivo
$bytes_sent = readfile($file_path);
error_log("Archivo servido - Bytes enviados: $bytes_sent");

// Registrar la descarga en logs con más detalles
$action_text = ($action === 'view') ? 'visualizado' : 'descargado';
error_log("Documento $action_text exitosamente - Usuario: {$usuario_id}, Archivo: {$file_name}, ID: {$documento_id}, Ruta: {$file_path}, Tamaño: {$file_size} bytes");

exit;
?>
